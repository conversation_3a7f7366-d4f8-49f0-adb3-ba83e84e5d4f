import { Injectable } from '@angular/core';
import { NotificationService } from './notification.service';

@Injectable({
  providedIn: 'root',
})
export class SimpleErrorHandlerService {
  constructor(private notificationService: NotificationService) {}

  handleError(error: any): void {
    const message = this.getErrorMessage(error);
    this.notificationService.showError(message);
  }

  showSuccess(message: string): void {
    this.notificationService.showSuccess(message);
  }

  showInfo(message: string): void {
    this.notificationService.showInfo(message);
  }
  showWarning(message: string): void {
    this.notificationService.showWarning(message);
  }
  showError(message: string): void {
    this.notificationService.showError(message);
  }

  private getErrorMessage(error: any): string {
    let backendMessage = '';

    // Extract the backend message from different possible structures
    if (typeof error === 'string') {
      backendMessage = error;
    } else if (error?.message) {
      backendMessage = error.message;
    } else if (error?.error?.message) {
      backendMessage = error.error.message;
    } else if (error?.error?.error_description) {
      backendMessage = error.error.error_description;
    } else if (error?.error?.Error) {
      backendMessage = error.error.Error;
    } else if (error?.Error) {
      backendMessage = error.Error;
    }

    // Map backend messages to user-friendly messages
    return this.mapToUserFriendlyMessage(backendMessage);
  }

  /**
   * Simple mapping of backend messages to user-friendly messages
   */
  private mapToUserFriendlyMessage(backendMessage: string): string {
    const message = backendMessage.toLowerCase().trim();

    // Login errors
    if (message.includes('invalid password')) {
      return 'Invalid password. Please try again.';
    }

    if (
      message.includes('user not found') ||
      message.includes('email not found')
    ) {
      return 'No account found with this email address. Please check your email or sign up.';
    }

    // Registration errors
    if (
      message.includes('email already exists') ||
      message.includes('user with this email already exists')
    ) {
      return 'An account with this email address already exists. Please use a different email or try logging in.';
    }

    // Account status errors
    if (message.includes('deleted') || message.includes('inactive')) {
      return 'Your account is inactive. Please contact support to reactivate your account.';
    }

    if (message.includes('locked')) {
      return 'Your account has been locked. Please try again later or contact support.';
    }

    // OTP errors
    if (message.includes('otp') || message.includes('verification code')) {
      if (message.includes('expired')) {
        return 'Verification code has expired. Please request a new one.';
      }
      if (message.includes('invalid') || message.includes('incorrect')) {
        return 'Invalid verification code. Please try again.';
      }
      return 'Verification code error. Please try again.';
    }

    // Password reset errors
    if (
      message.includes('reset') &&
      (message.includes('token') || message.includes('link'))
    ) {
      if (message.includes('expired')) {
        return 'Reset link has expired. Please request a new password reset.';
      }
      if (message.includes('invalid')) {
        return 'Invalid reset link. Please request a new password reset.';
      }
    }

    // Network/server errors
    if (message.includes('network') || message.includes('connection')) {
      return 'Network error. Please check your connection and try again.';
    }

    if (message.includes('server') || message.includes('internal')) {
      return 'Server error occurred. Please try again later.';
    }

    // Return cleaned up original message if no specific mapping found
    return this.cleanMessage(backendMessage);
  }

  /**
   * Cleans up the original message for better display
   */
  private cleanMessage(message: string): string {
    return message
      .replace(/\b(Error|Exception|Failed)\b/gi, '')
      .replace(/\s+/g, ' ')
      .trim()
      .replace(/^[^a-zA-Z]*/, '')
      .replace(/[.!]*$/, '.')
      .replace(/^./, (str) => str.toUpperCase());
  }
}
