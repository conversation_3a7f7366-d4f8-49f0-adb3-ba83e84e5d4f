import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

import { AuthService } from '../../Services/auth.service';
import { AuthStateService } from '../../Services/AuthState.service';
import { NotificationService } from '../../../../Core/Services/notification.service';
import {
  ForgotPasswordRequest,
  AuthState,
  AuthError,
  AuthErrorType,
} from '../../Models';

@Component({
  selector: 'app-forget-password',
  standalone: false,
  templateUrl: './forget-password.component.html',
  styleUrl: './forget-password.component.scss',
})
export class ForgetPasswordComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  forgotPasswordForm: FormGroup;
  logoLoaded = true; // Assume logo loads successfully by default

  // Authentication state
  isLoading = false;
  error: string | null = null;

  constructor(
    private authService: AuthService,
    private authState: AuthStateService,
    private formBuilder: FormBuilder,
    private router: Router,
    private notificationService: NotificationService,
  ) {
    this.forgotPasswordForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
    });
  }

  ngOnInit(): void {
    // Subscribe to authentication state for loading and error states
    this.authState.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((state: AuthState) => {
        this.isLoading = state.isLoading;
        this.error = state.error;

        // Show error messages
        if (state.error) {
          this.handleAuthError(state.error);
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onSendResetLink(): void {
    // Clear any previous errors
    this.authState.clearError();

    // Validate form first
    if (!this.forgotPasswordForm.valid) {
      this.handleFormValidation();
      return;
    }

    const forgotPasswordData: ForgotPasswordRequest =
      this.forgotPasswordForm.value;

    this.authService.forgotPassword(forgotPasswordData).subscribe({
      next: (response) => {
        if (response.isSuccess) {
          this.notificationService.showSuccess(
            'Password reset instructions have been sent to your email address. Please check your inbox.',
          );
          // Navigate to verify reset token page with email parameter
          this.router.navigate(['/auth/verify-reset-token'], {
            queryParams: { email: forgotPasswordData.email },
          });
        }
      },
      error: (error: AuthError) => {
        console.error('Forgot password failed:', error);
        this.handleAuthError(error.message, error.type);
      },
    });
  }

  onBackToLogin(): void {
    this.router.navigate(['/auth/login']);
  }

  onImageError(event: any): void {
    this.logoLoaded = false;
    console.log('Logo failed to load, showing fallback');
  }

  private markFormGroupTouched(): void {
    Object.keys(this.forgotPasswordForm.controls).forEach((key) => {
      const control = this.forgotPasswordForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Handles form validation errors
   */
  private handleFormValidation(): void {
    this.markFormGroupTouched();

    const emailControl = this.forgotPasswordForm.get('email');

    if (emailControl?.hasError('required')) {
      this.notificationService.showError('Email address is required.');
      return;
    }

    if (emailControl?.hasError('email')) {
      this.notificationService.showError('Please enter a valid email address.');
      return;
    }

    this.notificationService.showError(
      'Please check your input and try again.',
    );
  }

  /**
   * Handles authentication errors with specific messages
   */
  private handleAuthError(message: string, errorType?: AuthErrorType): void {
    switch (errorType) {
      case AuthErrorType.EMAIL_NOT_FOUND:
        this.notificationService.showError(
          'Email address not found. Please check your email and try again, or sign up for a new account.',
        );
        break;
      case AuthErrorType.USER_NOT_FOUND:
        this.notificationService.showError(
          'No account found with this email address. Please check your email or sign up.',
        );
        break;
      case AuthErrorType.NETWORK_ERROR:
        this.notificationService.showError(
          'Network connection error. Please check your internet connection and try again.',
        );
        break;
      case AuthErrorType.SERVER_ERROR:
        this.notificationService.showError(
          'Server error occurred. Please try again later.',
        );
        break;
      case AuthErrorType.VALIDATION_ERROR:
        this.notificationService.showError(
          message || 'Please check your input and try again.',
        );
        break;
      default:
        this.notificationService.showError(
          message || 'An unexpected error occurred. Please try again.',
        );
        break;
    }
  }

  // Getter methods for template
  get emailControl() {
    return this.forgotPasswordForm.get('email');
  }
}
